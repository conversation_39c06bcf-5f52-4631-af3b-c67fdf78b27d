from typing import Optional

from pydantic import BaseModel


class Motive(BaseModel):
    id: int
    name: str
    open: bool
    instructions: str
    hint: Optional[str] = (
        None  # TODO: is this something we need ? Should it be a string?
    )
    speciality_id: int

    min_age: Optional[float] = (
        None  # TODO: convert to int when config typing is neat (ex on config25)
    )
    max_age: Optional[int] = None
    should_transfer: bool = False
