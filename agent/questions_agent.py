from typing import Annotated, List

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from agent.tools.confirm_identity import confirm_identity
from models.clinics import ClinicConfig
from models.patients import Patient
from models.user_data import RunContext_T
from utils.prompts import build_global_system_prompt, build_name_prompt
from utils.time import format_opening_hours


def build_system_prompt(
    clinic_config: ClinicConfig, retrieved_patients: List[Patient]
) -> str:
    return f"""
        {build_global_system_prompt(clinic_config)}
        
        
        # Instructions:
       
        Your responsibilities are to answer any question the patient might have about the clinic and the medical practice. 
        
        ## 1. How to answer questions
        - Take the current conversation context into account to continue the conversation naturally and answer the questions.
        - You are a read-only agent, you can only answer questions and provide information.
            - You can't book, reschedule, cancel or modify an appointment
        - If the patient asks something unrelated, politely redirect them to one of the tasks above.
        
        ### **IMPORTANT** What you shouldn't do
        - **DO NOT** answer any medical questions. You are a medical assistant, not a doctor.
        - **NEVER** advise to contact the office directly or to call back
        - If you don't know the answer to a question about the clinic, don't invent anything, just say you don't know.
        
        ## 2. When answering a question
        - After answering a question, whether you managed to answer properly or not call the tool `handle_question` 
        - If you just answered a question, but the patient was doing something else before asking the question (like booking an appointment), mention the previous task and ask if the patient wants to continue with it.
            
        ## You knowledge base
        
        ### Clinic information:
        - Name: {clinic_config.name}
        - Address: {clinic_config.address}
        - Openings: {format_opening_hours(clinic_config.openings)}
        - Additional information: {clinic_config.additional_information}
        
        ### Patient information:
        If the patient asks information about their appointments, follow the instructions below:
        {build_name_prompt(retrieved_patients)}
        
        ### Prescription requests:
        If the patient has a requests about their prescription, end the call and tell them you will leave a note for the clinic's office
    """


class QuestionsAgent(BaseAgent):
    name = "questions"

    def __init__(self, clinic_config: ClinicConfig, retrieved_patients: List[Patient]):
        super().__init__(
            clinic_config=clinic_config,
            instructions=build_system_prompt(clinic_config, retrieved_patients),
            tools=[confirm_identity],
        )

    @function_tool()
    async def handle_question(
        self,
        context: RunContext_T,
        question: Annotated[
            str,
            Field(description="A concise description of the patient's question"),
        ],
        answer: Annotated[
            str,
            Field(description="The answer you gave to the patient"),
        ],
        has_answered_question: Annotated[
            bool,
            Field(description="Whether you have answered the question or not"),
        ],
    ) -> str:
        """
        Called when you have answered a question from the patient.
        """
        logger.info(
            f"Question: {question} | Answer: {answer} | Answered: {has_answered_question}"
        )

        if has_answered_question:
            context.userdata.is_call_successful = True

        return "If the patient was doing something else before asking the question (like booking an appointment), mention the previous task and ask if the patient wants to continue with it."
