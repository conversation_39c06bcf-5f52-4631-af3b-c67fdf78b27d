from agent.base_agent import BaseAgent
from models.clinics import ClinicConfig
from utils.prompts import build_global_system_prompt, build_greeting_prompt


def build_system_prompt(clinic_config: ClinicConfig) -> str:
    return f"""
        {build_global_system_prompt(clinic_config)}
        
        # Instructions:
        
        Your responsibilities are limited to the following:
    
       
        1. Start by greeting the patient to the clinic and asking them how you can help them.
            - {build_greeting_prompt(clinic_config.name)}
            - Then, ask the patient how you can help them today
        2. Once the patient has manifested their intent, call the 'change_intent' function with the appropriate intent.
        - If you're unsure about the patient's intent, ask clarifying questions to make a decision.
        - **Do not address or inquire about any other topics**. If the user asks something unrelated, politely redirect them to one of the tasks above.
    """


class WelcomeAgent(BaseAgent):
    name = "welcome"

    def __init__(self, clinic_config: ClinicConfig):
        super().__init__(
            clinic_config=clinic_config, instructions=build_system_prompt(clinic_config)
        )
