from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class AvailabilityStep(BaseModel):
    agenda_id: int
    equipment_agenda_id: Optional[str] = None
    # practitioner_agenda_id: Optional[int]
    start_date: datetime
    end_date: datetime
    visit_motive_id: int


class Availability(BaseModel):
    agenda_id: int
    equipment_agenda_id: Optional[str] = None
    practice_id: Optional[str] = None
    # practitioner_agenda_id: Optional[int]
    start_date: datetime
    end_date: datetime
    steps: List[AvailabilityStep] = []
    doctor_name: Optional[str] = None  # TODO: use object and merge with substitute
    substitute_doctor_name: Optional[str] = None
    # practitioner_agenda_id: null,
