from typing import Annotated, Optional

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from models.user_data import RunContext_T


@function_tool()
async def confirm_doctor_selection(
    context: RunContext_T,
    doctor_id: Annotated[
        Optional[int],
        Field(
            description="The doctor ID the patient wants to book with. None if the patient doesn't want a specific doctor."
        ),
    ],
) -> Optional[str]:
    """
    Called when the user confirms the doctor they want to book with
    """
    logger.info(f"Confirming doctor selection: {doctor_id}")  # noqa: F821

    if doctor_id is None:
        context.userdata.doctor = None
        return "Let's go to next step"

    if not context.userdata.patient_identity:
        return "Patient identity is not confirmed. Collect it again before finding availabilities."

    found_doctor = next(
        (a for a in context.userdata.clinic_config.agendas if a.id == doctor_id),
        None,
    )

    if not found_doctor:
        return f"Invalid doctor ID: {doctor_id}. Please try again."

    if (
        found_doctor.max_age is not None
        and context.userdata.patient_identity.age > found_doctor.max_age
    ):
        return f"The patient is too old ({context.userdata.patient_identity.age} vs max_age: {found_doctor.max_age}) to be treated by this doctor. Propose the patient to select another doctor."
    if (
        found_doctor.min_age is not None
        and context.userdata.patient_identity.age < found_doctor.min_age
    ):
        return f"The patient is too young ({context.userdata.patient_identity.age} vs min_age: {found_doctor.min_age}) to be treated by this doctor. Propose the patient to select another doctor."

    if found_doctor.should_transfer:
        return "This doctor requires special attention. Inform the patient that this doctor requires special attention. Forward the call to the reception by using `forward_call`"

    if found_doctor != context.userdata.doctor:
        context.userdata.availabilities = []

    context.userdata.doctor = found_doctor
    return "Now let's go to next step"
