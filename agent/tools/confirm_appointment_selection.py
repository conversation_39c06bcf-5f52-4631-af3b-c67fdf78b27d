from typing import Annotated

import sentry_sdk
from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from models.user_data import CallIntent, RunContext_T
from services.appointments import confirm_appointment


@function_tool()
async def confirm_appointment_selection(
    context: RunContext_T,
    appointment_id: Annotated[
        str,
        Field(
            description="The patient's appointment ID they want to act on (cancel, reschedule, delay)"
        ),
    ],
) -> str:
    """
    Called after you confirmed the appointment the patient wants to act on (cancel, reschedule, delay)
    """
    logger.info(f"Confirming appointment selection: {appointment_id}")

    patient = context.userdata.patient
    if not patient:
        return (
            "No patient retrieved, please confirm patient identity before proceeding."
        )

    appointment = next(
        (a for a in patient.appointments if a.id == appointment_id),
        None,
    )

    if not appointment:
        return f"Invalid appointment ID: {appointment_id}. Please try again."

    context.userdata.appointment = appointment

    if context.userdata.intent == CallIntent.DELAY:
        return "Proceed to next step"

    if context.userdata.intent == CallIntent.CONFIRM:
        await confirm_appointment(
            clinic_config=context.userdata.clinic_config,
            appointment_id=appointment.id,
        )
        context.userdata.is_call_successful = True
        await context.userdata.audio_player.play("./assets/ding.wav")
        return "Appointment is confirmed."

    appointment_motive = next(
        (
            m
            for m in context.userdata.clinic_config.motives
            if m.id == appointment.motive.id
        ),
        None,
    )
    context.userdata.motive = appointment_motive

    if not appointment_motive:
        return f"Invalid motive: {appointment.motive.name}. Please try again."
    logger.info(f"Found motive: {appointment_motive.name} ({appointment_motive.id})")

    if appointment.linked_appointment_id:
        linked_appointment = next(
            (
                a
                for a in patient.appointments
                if a.id == appointment.linked_appointment_id
            ),
            None,
        )
        if linked_appointment:
            logger.info(f"Found linked appointment: {linked_appointment.motive}")
            context.userdata.secondary_motive = linked_appointment.motive
        else:
            logger.warning(f"Could not find linked appointment: {appointment}")
            sentry_sdk.set_context("appointment", appointment.model_dump(mode="json"))
            sentry_sdk.set_extra(
                "appointments",
                [a.model_dump(mode="json") for a in patient.appointments],
            )
            sentry_sdk.capture_message("Could not find linked appointment")

    appointment_doctor = next(
        (
            a
            for a in context.userdata.clinic_config.agendas
            if a.id == appointment.agenda_id
        ),
        None,
    )
    # TODO: for now, not finding the doctor is not blocking. To improve it, we should add confirm_doctor_selection to reschedule agent
    context.userdata.doctor = appointment_doctor
    if appointment_doctor:
        logger.info(
            f"Found doctor: {appointment_doctor.name} ({appointment_doctor.id})"
        )

    # Forward the call if the doctor should be transferred
    if context.userdata.intent == CallIntent.RESCHEDULE and (
        appointment_motive.should_transfer
        or (appointment_doctor and appointment_doctor.should_transfer)
    ):
        return "This appointment requires special attention. Inform the patient that this appointment requires special attention. Forward the call to the reception by using `forward_call`."

    # We cannot reschedule a closed motive
    if (
        context.userdata.intent in [CallIntent.RESCHEDULE, CallIntent.CANCEL]
        and not appointment.motive.open
    ):
        return """
            Tell the patient this motive needs specific attention from the clinic personnel and cannot be rescheduled or cancelled. 
            Propose to leave a note to the reception for the patient to be contacted by the clinic. 
        """

    if context.userdata.intent == CallIntent.CANCEL:
        return "Now let's confirm the cancellation reason"

    if (
        not context.userdata.motive
        or context.userdata.motive.id != appointment_motive.id
    ):
        context.userdata.motive = appointment_motive
        context.userdata.availabilities = []

    return "Now let's find availabilities for this motive"
