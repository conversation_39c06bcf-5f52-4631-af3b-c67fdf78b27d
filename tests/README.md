# Tests

This directory contains unit tests for the livekit-poc project.

## Running Tests

To run all tests:
```bash
uv run python -m pytest tests/ -v
```

To run a specific test file:
```bash
uv run python -m pytest tests/test_is_clinic_open.py -v
```

To run a specific test method:
```bash
uv run python -m pytest tests/test_is_clinic_open.py::TestIsClinicOpen::test_clinic_open_during_business_hours -v
```

## Test Coverage

### `test_is_clinic_open.py`

Comprehensive unit tests for the `is_clinic_open` function from `utils.clinics` module.

**Test scenarios covered:**
- ✅ Normal business hours (clinic open)
- ✅ Outside business hours (clinic closed)
- ✅ Disabled schedules (clinic closed)
- ✅ No schedule for the day (clinic closed)
- ✅ Overnight hours before midnight (clinic open)
- ✅ Overnight hours after midnight (clinic open)
- ✅ Overnight hours outside range (clinic closed)
- ✅ Multiple time ranges per day (both ranges)
- ✅ Boundary conditions (exactly at start/end times)
- ✅ Empty ranges with enabled schedule (clinic closed)
- ✅ Multiple days in opening hours list
- ✅ Empty openings list (clinic closed)
- ✅ Edge cases (malformed ranges, very short ranges)
- ✅ Midnight edge case for overnight hours
- ✅ Weekday conversion logic

**Key features tested:**
- Time zone handling (Europe/Paris)
- Weekday conversion (Monday=1, Sunday=7)
- Time range parsing and comparison
- Overnight hour logic (e.g., 22:00-02:00)
- Multiple time ranges per day
- Enabled/disabled schedule handling
- Boundary conditions and edge cases

## Continuous Integration

Tests are automatically run on every pull request and push to main via GitHub Actions. The workflow includes:

- **Linting** with ruff
- **Type checking** with mypy
- **Unit testing** with pytest

You can view the test results in the GitHub Actions tab of the repository.

## Dependencies

The tests use:
- `pytest` - Testing framework
- `unittest.mock` - Mocking for datetime
- `pydantic` - Model validation (OpeningHour)
- `zoneinfo` - Timezone handling

## Test Structure

Tests follow the AAA pattern:
- **Arrange**: Set up test data and mocks
- **Act**: Call the function under test
- **Assert**: Verify the expected outcome

Each test method is focused on a specific scenario and includes descriptive docstrings explaining what is being tested.
