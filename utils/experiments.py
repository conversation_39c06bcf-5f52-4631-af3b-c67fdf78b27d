import os
import random
from typing import List

EXPERIMENTS = {
    "all": {"cartesia-tts": 20},
    "config_demo_1_v3": {"cartesia-tts": 0},
    "config_demo_2_v3": {"cartesia-tts": 0},
    "config_demo_3_v3": {"cartesia-tts": 0},
    "config_demo_4_v3": {"cartesia-tts": 0},
    "config_demo_maiia_v3": {"cartesia-tts": 0},
    "config172": {"cartesia-tts": 0},
    "config173": {"cartesia-tts": 100},
    "config174": {"cartesia-tts": 0},
    "config30": {"cartesia-tts": 0},
}


def get_experiments(config_id: str, is_dev: bool) -> List[str]:
    dev_experiments = os.getenv("DEV_EXPERIMENTS")
    if is_dev and dev_experiments:
        return dev_experiments.split(",")

    experiments = dict(EXPERIMENTS.get("all", {}))
    experiments.update(EXPERIMENTS.get(config_id, {}))
    random_number = random.randint(1, 100)
    return [exp for exp, trigger in experiments.items() if random_number <= trigger]
