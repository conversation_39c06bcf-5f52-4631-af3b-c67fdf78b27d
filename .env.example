# LiveKit Configuration (Required)
#AGENT_NAME=livekit-agent # Only for SIP calls
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=
LIVEKIT_URL=

# OpenAI Configuration (Required if not using Azure)
OPENAI_API_KEY=

# Deepgram Configuration
DEEPGRAM_API_KEY=

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_ENDPOINT=
OPENAI_API_VERSION=

# Azure TTS
AZURE_SPEECH_REGION=westeurope
AZURE_SPEECH_KEY=

# Cartesia TTS
CARTESIA_API_KEY=
CARTESIA_VOICE_ID=

# Docto Scrape
SECRET_DOCTOLIB_TOKEN=
SECRET_DOCTOLIB_URL="https://doctolib-secret-scrape-global.onrender.com/api/v2"
#SECRET_DOCTOLIB_URL="http://127.0.0.1:8080/api/v2"

# Supabase
SUPABASE_URL=
SUPABASE_KEY=

# Twilio
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=

# Sentry
SENTRY_DSN="https://<EMAIL>/****************"

# DEV
DEV_PHONE_NUMBER=
DEV_CONFIG_ID=
DEV_EXPERIMENTS=
