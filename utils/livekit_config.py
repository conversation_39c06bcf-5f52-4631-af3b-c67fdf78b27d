import asyncio
from typing import Callable, Coroutine

import sentry_sdk
from livekit.agents import (
    AgentSession,
    ChatMessage,
    ConversationItemAddedEvent,
    ErrorEvent,
    MetricsCollectedEvent,
    UserStateChangedEvent,
    metrics
)
from livekit.agents.metrics import UsageCollector
from livekit.rtc import (
    ConnectionQuality,
    DisconnectReason,
    Participant,
    ParticipantKind,
    RemoteParticipant
)
from loguru import logger

from models.errors import ErrorType
from models.user_data import UserData
from services.calls import report_call
from utils.latency_logger import LatencyLogger


# LiveKit event handlers
def handle_conversation_item_added(event: ConversationItemAddedEvent) -> None:
    if isinstance(event.item, ChatMessage):
        logger.info(
            f"Message: [{event.item.role.capitalize()}] {event.item.content[0]}"
        )


def handle_user_away(
    session: AgentSession[UserData],
) -> Callable[[UserStateChangedEvent], None]:
    def on_user_away(event: UserStateChangedEvent) -> None:
        if event.new_state == "away":
            if session.agent_state in ["thinking", "speaking"]:
                sentry_sdk.capture_message(  # TODO: Check if this happens
                    "User is flagged as away while agent is speaking"
                )
                return
            logger.info("User is away")
            session.say(
                "Êtes-vous toujours là ? Je suis à votre écoute et prête à continuer la conversation."
            )
            # session.generate_reply( # TODO: This doesn't work everytime. I'm not sure why, probably because we can't await
            #     instructions="Politely ask the user 'Are you still there', then tell them you're listening and propose to continue the conversation"
            # )
            session._update_user_state("listening")

    return on_user_away


def handle_connection_quality_change(
    session: AgentSession[UserData],
) -> Callable[[Participant, ConnectionQuality], None]:
    def on_connection_quality_change(
        _participant: Participant, quality: ConnectionQuality
    ) -> None:
        # TODO: FIXME fix those type errors
        logger.info(f"Connection quality changed: {ConnectionQuality.Name(quality)}")  # type: ignore
        if quality in [ConnectionQuality.QUALITY_POOR, ConnectionQuality.QUALITY_LOST]:  # type: ignore
            logger.warning("Quality is poor, generating reply")
            session.say(
                "J'ai du mal à vous entendre, pourriez-vous répéter s'il-vous-plaît ?"
            )
            # session.generate_reply( # TODO: This doesn't work everytime. I'm not sure why, probably because we can't await
            #     instructions="Politely tell the user you don't hear them because of the network quality."
            # )

    return on_connection_quality_change


def handle_session_end(
    session: AgentSession[UserData],
) -> Callable[[RemoteParticipant], None]:
    def on_session_end(participant: RemoteParticipant) -> None:
        logger.info(
            f"Participant left ({participant.sid} | {ParticipantKind.Name(participant.kind)} | {DisconnectReason.Name(participant.disconnect_reason)} | {participant.attributes})"  # type: ignore
        )
        asyncio.create_task(session.userdata.audio_player.aclose())
        asyncio.create_task(report_call(session.userdata, session.history))

    return on_session_end


def handle_metrics_collected(
    usage_collector: UsageCollector, latency_logger: LatencyLogger
) -> Callable[[MetricsCollectedEvent], None]:
    def on_metrics_collected(ev: MetricsCollectedEvent) -> None:
        metrics.log_metrics(ev.metrics)
        latency_logger.aggregate_metrics(ev.metrics)
        usage_collector.collect(ev.metrics)

    return on_metrics_collected


def handle_log_usage(
    usage_collector: UsageCollector,
) -> Callable[[], Coroutine[None, None, None]]:
    async def on_log_usage() -> None:
        summary = usage_collector.get_summary()
        logger.info(f"Usage: {summary}")

    return on_log_usage


def handle_error(session: AgentSession[UserData]) -> Callable[[ErrorEvent], None]:
    def on_error(ev: ErrorEvent) -> None:
        if ev.error.recoverable:  # Recoverable errors can be ignored
            logger.error(ev.error)
            return

        sentry_sdk.set_tag("error_type", ErrorType.LIVEKIT_SESSION_ERROR.value)
        sentry_sdk.set_extra("error_event", ev)
        sentry_sdk.capture_exception(ev.error)  # type: ignore
        session.generate_reply(
            instructions="Some technical error occured. Politely ask the patient to repeat what they just said.",
            allow_interruptions=True,
        )

    return on_error
