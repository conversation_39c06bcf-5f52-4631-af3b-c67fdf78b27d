import os

from lib.http_client import HTTPClient

SECRET_DOCTOLIB_TOKEN = os.getenv("SECRET_DOCTOLIB_TOKEN")


class N8NClient(HTTPClient):
    def __init__(self) -> None:
        if not SECRET_DOCTOLIB_TOKEN:
            raise ValueError("Missing Doctolib credentials")

        super().__init__(
            base_url="https://n8n-self-hosted-vocca.onrender.com/webhook",
            headers={"Authorization": f"Bearer {SECRET_DOCTOLIB_TOKEN}"},
        )


n8n_client = N8NClient()
