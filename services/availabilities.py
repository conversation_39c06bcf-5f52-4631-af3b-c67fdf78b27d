from datetime import date
from typing import List, Optional

import sentry_sdk
from loguru import logger

from lib.booking_provider_client import booking_provider_client
from models.availabilities import Availability, AvailabilityStep
from models.clinics import ClinicConfig
from models.errors import ErrorType
from models.motives import Motive


async def fetch_next_availabilities(
    clinic_config: ClinicConfig,
    motive: Motive,
    secondary_motive: Optional[Motive],
    practice_id: Optional[str],
    doctor_agenda_id: Optional[int],
    search_date: date,
    with_substitute: bool = True,
) -> List[Availability]:
    motives = [motive]
    if secondary_motive:
        motives.append(secondary_motive)
    motives_str = " + ".join([m.name for m in motives])

    agendas = clinic_config.agendas
    if practice_id:
        agendas = [
            agenda
            for agenda in agendas
            if not practice_id or agenda.practice_id == practice_id
        ]
    if doctor_agenda_id:
        agendas = [agenda for agenda in agendas if agenda.id == doctor_agenda_id]

    agenda_ids = [agenda.id for agenda in agendas]

    logger.info(
        f"Finding availabilities for {motives_str} on {search_date} for agendas {[a.name for a in agendas]}"
    )

    # TODO: merge both availabilities calls into a single one (and the model casting as well)
    availabilities: List[Availability] = []

    if secondary_motive:
        res = await booking_provider_client.post(
            "appointments/availabilities-steps",
            {
                "config": clinic_config.config_id,
                "day": str(search_date),
                "steps": [
                    {
                        "agenda_ids": agenda_ids,
                        "visit_motive_id": motive.id,
                    },
                    {
                        "agenda_ids": agenda_ids,
                        "visit_motive_id": secondary_motive.id,
                    },
                ],
            },
        )

        if res is None or len(res) == 0:
            logger.warning(f"Did not find availabilities for {motives_str}")
            return []

        for a in res:
            steps = a.get("steps", [])
            main_availability = steps[0]

            availabilities.append(
                Availability(
                    agenda_id=main_availability.get("agenda_id"),
                    equipment_agenda_id=main_availability.get("equipment_agenda_id"),
                    practice_id=main_availability.get("practice_id"),
                    start_date=a.get("start_date"),
                    end_date=a.get("end_date"),
                    doctor_name=(
                        main_availability.get("medecin")
                        if a.get("practitioner_agenda_id")
                        else None
                    ),  # TODO: format API call to return correct formatting directly within booking provider
                    substitute_doctor_name=(
                        main_availability.get("substitute") or {}
                    ).get("name"),
                    steps=[AvailabilityStep(**step) for step in steps],
                )
            )
    else:
        res = await booking_provider_client.post(
            "appointments/availabilities",
            {
                "config": clinic_config.config_id,
                "day": str(search_date),
                "agenda_id": agenda_ids,
                "visit_motive_id": motive.id,
                "with_substitute": with_substitute,
            },
        )

        if res is None or len(res) == 0:
            logger.warning(f"Did not find availabilities for {motives_str}")
            return []

        for a in res:
            if "agenda_id" not in a:
                logger.error(f"Missing agenda_id in availability: {a}")
                sentry_sdk.set_tag("error_type", ErrorType.DATA_CONSISTENCY.value)
                sentry_sdk.set_context("availability", a)
                sentry_sdk.set_context("availabilities", res)
                sentry_sdk.capture_message("Wrong availability format")
                continue
            availabilities.append(
                Availability(
                    agenda_id=a.get("agenda_id"),
                    equipment_agenda_id=a.get("equipment_agenda_id"),
                    practice_id=a.get("practice_id"),
                    start_date=a.get("start_date"),
                    end_date=a.get("end_date"),
                    doctor_name=(
                        a.get("medecin") if a.get("practitioner_agenda_id") else None
                    ),  # TODO: format API call to return correct formatting directly within booking provider
                    substitute_doctor_name=(a.get("substitute") or {}).get("name"),
                )
            )

    logger.info(f"Found {len(availabilities)} availabilities for {motives_str}")
    return availabilities
