import phonenumbers
import sentry_sdk
from phonenumbers import NumberParseException, PhoneNumberType, number_type

from models.errors import ErrorType


def is_valid_french_mobile_phone_number(phone_number: str) -> bool:
    try:
        parsed_number = phonenumbers.parse(phone_number, "FR")
        if not phonenumbers.is_valid_number(parsed_number):
            return False
        ntype = number_type(parsed_number)
        return ntype in (PhoneNumberType.MOBILE, PhoneNumberType.FIXED_LINE_OR_MOBILE)
    except NumberParseException:
        return False


def format_phone_number(phone_number: str) -> str:
    try:
        parsed_number = phonenumbers.parse(phone_number, "FR")
        return phonenumbers.format_number(
            parsed_number, phonenumbers.PhoneNumberFormat.E164
        )
    except Exception as e:
        sentry_sdk.set_tag("error_type", ErrorType.PHONE_NUMBER_VALIDATION.value)
        sentry_sdk.capture_exception(e)
        return phone_number
