import os

import sentry_sdk
from livekit.agents import AutoSubscribe, JobContext
from livekit.rtc import ParticipantKind

from models.calls import CallConfig
from utils.phone_numbers import format_phone_number


def get_dev_call_config(call_id: str) -> CallConfig:
    config_id = os.getenv("DEV_CONFIG_ID")
    caller_phone_number = os.getenv("DEV_PHONE_NUMBER")

    if not config_id or not caller_phone_number:
        raise ValueError("Missing dev config credentials")

    return CallConfig(
        call_id=call_id,
        livekit_call_id=call_id,
        is_dev=True,
        clinic_phone_number="",
        caller_phone_number=os.getenv("DEV_PHONE_NUMBER", ""),
        voice_speed=1.7,
    )


async def get_call_config(is_dev: bool, ctx: JobContext) -> CallConfig:
    if is_dev:
        return get_dev_call_config(f"dev_{ctx.job.id}")

    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    participant = await ctx.wait_for_participant(
        kind=[ParticipantKind.PARTICIPANT_KIND_SIP]
    )
    attrs = participant.attributes

    # TODO: should we use livekit_call_id for production instead?
    #  For now, we need twilio's for call recordings
    livekit_call_id = attrs.get("sip.callID")
    call_id = attrs.get("sip.twilio.callSid")
    caller_phone_number = format_phone_number(attrs.get("sip.phoneNumber", ""))
    clinic_phone_number = attrs.get("sip.trunkPhoneNumber")

    sentry_sdk.set_tag("call_id", call_id)
    sentry_sdk.set_tag("livekit_call_id", livekit_call_id)

    # TODO: temp. Just checking this won't break anything (calls without these params)
    if not call_id or not caller_phone_number or not clinic_phone_number:
        sentry_sdk.set_context("call_attrs", attrs)
        sentry_sdk.capture_message("Missing call data")

    return CallConfig(
        call_id=call_id,  # type: ignore
        livekit_call_id=livekit_call_id,  # type: ignore
        caller_phone_number=caller_phone_number,
        clinic_phone_number=clinic_phone_number,  # type: ignore
    )
