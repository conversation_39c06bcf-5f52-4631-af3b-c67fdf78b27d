from typing import Annotated, List

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from agent.tools.confirm_appointment_selection import confirm_appointment_selection
from agent.tools.confirm_identity import confirm_identity
from models.clinics import ClinicConfig, DelayStrategy
from models.patients import Patient
from models.user_data import RunContext_T
from services.appointments import cancel_appointment, notify_delay
from utils.delay import get_max_delay_minutes
from utils.prompts import (
    END_OF_FLOW_PROMPT,
    build_global_system_prompt,
    build_name_prompt,
)


# TODO: not fan of the "no need to ask", we should definitely build something more robust
def build_system_prompt(
    clinic_config: ClinicConfig, retrieved_patients: List[Patient]
) -> str:
    if clinic_config.delay_strategy == DelayStrategy.FORWARD_CALL:
        return build_global_system_prompt(clinic_config)

    return f"""
        {build_global_system_prompt(clinic_config)}
        
        # Instructions:
       
        Your goal is to **guide the user through notifying the clinic that the patient will be late** from start to finish, following the steps below. 
        - Begin the flow immediately (do not introduce yourself with extra chit-chat) and lead the patient through each step, while adhering to the above guidelines. 
        - The delay notification is only successful when you have called the `confirm_delay` tool with all required details and the user has confirmed everything.
            - If the tool is not called, the delay notification is not considered successful, try to figure out what is blocking you and try to resolve it
            
        ### 1. Patient identity collection
        {build_name_prompt(retrieved_patients)}
        **Important**: No need to ask if the patient already came to the clinic as we are in the delay notification flow.
        
        ### 2. Appointment selection
        Once the patient is identified, you should see the current pending appointments they have.
        - If the patient has only one appointment, ask them to confirm that it is the one they want to notify a delay for.
        - If the patient has no appointment, stop the flow and inform them that there is no appointment to notify a delay for.
        - If the patient has multiple appointments, ask them to confirm the one they want to notify a delay for.
        - Once the appointment to notify a delay for is confirmed, call the `confirm_appointment_selection` tool with the appointment ID.
        
        ### 3. Delay duration 
        Ask the patient how long they will be late for their appointment.
        
        ### 4. Delay notification confirmation
        Once the patient has selected the appointment and how long they will be late, confirm the delay with them.
            - If the patient confirms, call the `confirm_delay` tool with the appointment ID and the delay in minutes.
            - If the patient declines, ask them how they want to proceed.
        Once the notification is confirmed, inform the patient that the clinic has been notified.
        
        ### 5. End of flow
        {END_OF_FLOW_PROMPT}
    """


class DelayAgent(BaseAgent):
    name = "delay"

    def __init__(self, clinic_config: ClinicConfig, retrieved_patients: List[Patient]):
        self.clinic_config = clinic_config
        super().__init__(
            clinic_config=clinic_config,
            instructions=build_system_prompt(clinic_config, retrieved_patients),
            tools=[confirm_identity, confirm_appointment_selection],
        )

    async def on_enter(self) -> None:
        logger.info(f"Starting {self.__class__.__name__} workflow")

        if self.clinic_config.delay_strategy == DelayStrategy.FORWARD_CALL:
            await self.session.generate_reply(
                instructions="Call the `forward_call` tool."
            )
            return

        await super().on_enter()

    @function_tool()
    async def confirm_delay(
        self,
        context: RunContext_T,
        delay_minutes: Annotated[
            int,
            Field(description="The number of minutes the patient will be late"),
        ],
    ) -> str:
        """
        Called when the user confirms the appointment they want to notify a delay for and how long they will be late.
        """
        if context.userdata.appointment is None:
            return "No appointment selected for delay notification. Please select an appointment before confirming delay."

        logger.info(
            f"Confirming delay of for {delay_minutes} minutes for appointment {context.userdata.appointment.id} (behavior: {context.userdata.clinic_config.delay_strategy})"
        )

        if context.userdata.clinic_config.delay_strategy == DelayStrategy.CREATE_TASK:
            context.userdata.is_call_successful = True
            return "Tell the patient you will leave a note to the reception, then end the call with `end_call` and `should_leave_note` set to `True`."

        max_delay = get_max_delay_minutes(
            context.userdata.clinic_config, context.userdata.appointment
        )

        if max_delay and delay_minutes > max_delay:
            await cancel_appointment(
                context.userdata.clinic_config,
                context.userdata.appointment.id,
                f"RDV annulé, retard {delay_minutes} min.",
            )
            return "The delay is too long, and the clinic has canceled the appointment. Please inform the patient politely and ask if they would like to reschedule."

        await notify_delay(
            context.userdata.clinic_config,
            context.userdata.appointment.id,
            delay_minutes,
        )
        context.userdata.is_call_successful = True
        await context.userdata.audio_player.play("./assets/ding.wav")

        message = "The clinic has been notified."
        if max_delay:
            message += f" Inform them that the clinic may cancel if the delay exceeds {max_delay} minutes."
        return message
