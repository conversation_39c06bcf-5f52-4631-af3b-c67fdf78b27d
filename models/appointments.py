from datetime import datetime
from typing import Optional

from pydantic import BaseModel

from models.motives import Motive


class Appointment(BaseModel):
    id: str
    patient_id: str
    agenda_id: int

    start_date: datetime
    end_date: datetime

    motive: Motive

    linked_appointment_id: Optional[str] = None

    def __str__(self) -> str:
        return f"{self.motive.name} on {self.start_date} (appointment_id: {self.id})"
