import logging
import sys

from loguru import logger

from models.calls import CallConfig


def configure_logger(call_config: CallConfig) -> None:
    # Silence noisy loggers
    hpack_logger = logging.getLogger("hpack.hpack")
    hpack_logger.setLevel(logging.INFO)

    hpack_table_logger = logging.getLogger("hpack.table")
    hpack_table_logger.setLevel(logging.INFO)

    # Add call_id to loguru
    logger.remove()
    logger.add(
        sys.stdout,
        format=f"{{time:YYYY-MM-DDTHH:mm:ss.SSS}} - <level>{{level}}</level> - <yellow>{call_config.call_id}</yellow> | {{message}}",
        colorize=True,
    )
    logger.info(f"Call Config: {call_config}")
