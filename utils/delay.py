from models.appointments import Appointment
from models.clinics import ClinicConfig


def get_max_delay_minutes(
    clinic_config: ClinicConfig, appointment: Appointment
) -> int | None:
    """
    Returns the maximum allowed delay in minutes for the given appointment based on the clinic configuration.
    If there is no limit, returns None.
    """
    speciality = next(
        (
            s
            for s in clinic_config.specialities
            if s.id == appointment.motive.speciality_id
        ),
        None,
    )
    if speciality is None:
        return None

    return speciality.late_arrival_limit
