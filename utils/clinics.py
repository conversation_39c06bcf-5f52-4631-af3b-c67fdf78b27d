from datetime import datetime
from typing import List
from zoneinfo import ZoneInfo

from models.clinics import ClinicConfig, OpeningHour
from models.motives import Motive


def format_clinics(clinics: list[ClinicConfig]) -> str:
    return "\n".join(
        [
            f"  - {c.name} (practice_id: {c.practice_id}, config_id: {c.config_id})"
            for c in clinics
        ]
    )


def is_motive_available_at_clinic(motive: Motive, practice_id: str) -> bool:
    if practice_id == "global":
        return True

    if not motive.practice_ids:
        return True

    return practice_id in motive.practice_ids


# TODO: `OpeningHour` model could use a refacto so it's easier to check schedules
def is_clinic_open(openings: List[OpeningHour]) -> bool:
    now = datetime.now(tz=ZoneInfo("Europe/Paris"))
    current_day = now.weekday() + 1  # Monday=1, Sunday=7
    current_time = now.time()

    # Find today's schedule
    today_schedule = next(
        (day for day in openings if day.day == current_day and day.enabled), None
    )

    if not today_schedule:
        return False

    # Check if current time falls within any range
    for time_range in today_schedule.ranges:
        if len(time_range) >= 2:
            start = datetime.strptime(time_range[0], "%H:%M").time()
            end = datetime.strptime(time_range[1], "%H:%M").time()

            if start <= end:  # Same day
                if start <= current_time <= end:
                    return True
            else:  # Overnight (e.g., 22:00-02:00)
                if current_time >= start or current_time <= end:
                    return True

    return False
