"""
Unit tests for the is_clinic_open function from utils.clinics module.

This module tests various scenarios for clinic opening hours including:
- Different days of the week
- Multiple time ranges per day
- Enabled/disabled schedules
- Overnight hours (e.g., 22:00-02:00)
- Edge cases and boundary conditions
"""

import unittest
from datetime import datetime
from unittest.mock import patch
from zoneinfo import ZoneInfo

from models.clinics import OpeningHour
from utils.clinics import is_clinic_open


class TestIsClinicOpen(unittest.TestCase):
    """Test cases for the is_clinic_open function."""

    def setUp(self):
        """Set up test fixtures."""
        # Create sample opening hours for testing
        self.monday_opening = OpeningHour(
            day=1,  # Monday
            ranges=[("09:00", "12:00"), ("14:00", "18:00")],
            enabled=True,
        )

        self.tuesday_opening = OpeningHour(
            day=2,  # Tuesday
            ranges=[("08:00", "17:00")],
            enabled=True,
        )

        self.wednesday_disabled = OpeningHour(
            day=3,  # Wednesday
            ranges=[("09:00", "17:00")],
            enabled=False,
        )

        self.thursday_overnight = OpeningHour(
            day=4,  # Thursday
            ranges=[("22:00", "02:00")],  # Overnight hours
            enabled=True,
        )

        self.friday_empty_ranges = OpeningHour(
            day=5,  # Friday
            ranges=[],
            enabled=True,
        )

        self.saturday_single_range = OpeningHour(
            day=6,  # Saturday
            ranges=[("10:00", "16:00")],
            enabled=True,
        )

    @patch("utils.clinics.datetime")
    def test_clinic_open_during_business_hours(self, mock_datetime):
        """Test that clinic is open during normal business hours."""
        # Mock Monday at 10:30 AM
        mock_now = datetime(
            2024, 1, 1, 10, 30, tzinfo=ZoneInfo("Europe/Paris")
        )  # Monday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_closed_outside_business_hours(self, mock_datetime):
        """Test that clinic is closed outside business hours."""
        # Mock Monday at 1:00 PM (between ranges)
        mock_now = datetime(
            2024, 1, 1, 13, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Monday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening]

        result = is_clinic_open(openings)
        self.assertFalse(result)

    @patch("utils.clinics.datetime")
    def test_clinic_closed_when_disabled(self, mock_datetime):
        """Test that clinic is closed when schedule is disabled."""
        # Mock Wednesday at 10:00 AM (within range but disabled)
        mock_now = datetime(
            2024, 1, 3, 10, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Wednesday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.wednesday_disabled]

        result = is_clinic_open(openings)
        self.assertFalse(result)

    @patch("utils.clinics.datetime")
    def test_clinic_closed_no_schedule_for_day(self, mock_datetime):
        """Test that clinic is closed when no schedule exists for the day."""
        # Mock Sunday (day 7, but we only have Monday schedule)
        mock_now = datetime(
            2024, 1, 7, 10, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Sunday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening]

        result = is_clinic_open(openings)
        self.assertFalse(result)

    @patch("utils.clinics.datetime")
    def test_clinic_open_overnight_hours_before_midnight(self, mock_datetime):
        """Test overnight hours - clinic open before midnight."""
        # Mock Thursday at 23:00 (11 PM)
        mock_now = datetime(
            2024, 1, 4, 23, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Thursday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.thursday_overnight]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_open_overnight_hours_after_midnight(self, mock_datetime):
        """Test overnight hours - clinic open after midnight."""
        # Mock Thursday at 01:00 AM (next day but within overnight range)
        mock_now = datetime(
            2024, 1, 4, 1, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Thursday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.thursday_overnight]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_closed_overnight_hours_outside_range(self, mock_datetime):
        """Test overnight hours - clinic closed outside overnight range."""
        # Mock Thursday at 03:00 AM (outside 22:00-02:00 range)
        mock_now = datetime(
            2024, 1, 4, 3, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Thursday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.thursday_overnight]

        result = is_clinic_open(openings)
        self.assertFalse(result)

    @patch("utils.clinics.datetime")
    def test_clinic_open_multiple_ranges_first_range(self, mock_datetime):
        """Test clinic open during first of multiple ranges."""
        # Mock Monday at 10:00 AM (first range: 09:00-12:00)
        mock_now = datetime(
            2024, 1, 1, 10, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Monday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_open_multiple_ranges_second_range(self, mock_datetime):
        """Test clinic open during second of multiple ranges."""
        # Mock Monday at 15:00 PM (second range: 14:00-18:00)
        mock_now = datetime(
            2024, 1, 1, 15, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Monday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_boundary_conditions_start_time(self, mock_datetime):
        """Test boundary condition - exactly at start time."""
        # Mock Monday at 09:00 AM (exactly at start)
        mock_now = datetime(2024, 1, 1, 9, 0, tzinfo=ZoneInfo("Europe/Paris"))  # Monday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_boundary_conditions_end_time(self, mock_datetime):
        """Test boundary condition - exactly at end time."""
        # Mock Monday at 12:00 PM (exactly at end)
        mock_now = datetime(
            2024, 1, 1, 12, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Monday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_empty_ranges_enabled(self, mock_datetime):
        """Test clinic with empty ranges but enabled schedule."""
        # Mock Friday at 10:00 AM (empty ranges but enabled)
        mock_now = datetime(
            2024, 1, 5, 10, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Friday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.friday_empty_ranges]

        result = is_clinic_open(openings)
        self.assertFalse(result)

    @patch("utils.clinics.datetime")
    def test_clinic_multiple_days_schedules(self, mock_datetime):
        """Test with multiple days in opening hours list."""
        # Mock Tuesday at 10:00 AM
        mock_now = datetime(
            2024, 1, 2, 10, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Tuesday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.monday_opening, self.tuesday_opening, self.wednesday_disabled]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_empty_openings_list(self, mock_datetime):
        """Test with empty openings list."""
        # Mock any day
        mock_now = datetime(2024, 1, 1, 10, 0, tzinfo=ZoneInfo("Europe/Paris"))
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = []

        result = is_clinic_open(openings)
        self.assertFalse(result)

    @patch("utils.clinics.datetime")
    def test_clinic_malformed_time_range(self, mock_datetime):
        """Test with malformed time range (less than 2 elements)."""
        # Mock Monday at 10:00 AM
        mock_now = datetime(
            2024, 1, 1, 10, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Monday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        # Create a valid OpeningHour but manually modify ranges to test edge case
        # Since Pydantic validates the model, we'll test with an empty range instead
        malformed_opening = OpeningHour(
            day=1,
            ranges=[],  # Empty ranges list
            enabled=True,
        )

        openings = [malformed_opening]

        result = is_clinic_open(openings)
        self.assertFalse(result)

    def test_weekday_conversion(self):
        """Test that weekday conversion works correctly (Monday=1, Sunday=7)."""
        # The function uses now.weekday() + 1, where Monday=0 becomes 1
        # This test verifies the day matching logic indirectly through other tests
        # but we can test the conversion explicitly by checking our test data

        # Monday should be day 1
        self.assertEqual(self.monday_opening.day, 1)
        # Tuesday should be day 2
        self.assertEqual(self.tuesday_opening.day, 2)
        # etc.

    @patch("utils.clinics.datetime")
    def test_clinic_open_exactly_at_midnight_overnight(self, mock_datetime):
        """Test overnight hours - clinic open exactly at midnight."""
        # Mock Thursday at 00:00 (midnight)
        mock_now = datetime(
            2024, 1, 4, 0, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Thursday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        openings = [self.thursday_overnight]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_single_minute_range(self, mock_datetime):
        """Test with a very short time range (single minute)."""
        # Mock Saturday at 10:00 AM
        mock_now = datetime(
            2024, 1, 6, 10, 0, tzinfo=ZoneInfo("Europe/Paris")
        )  # Saturday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        # Create a very short opening hour (10:00-10:01)
        short_opening = OpeningHour(
            day=6,  # Saturday
            ranges=[("10:00", "10:01")],
            enabled=True,
        )

        openings = [short_opening]

        result = is_clinic_open(openings)
        self.assertTrue(result)

    @patch("utils.clinics.datetime")
    def test_clinic_just_after_short_range(self, mock_datetime):
        """Test just after a very short time range."""
        # Mock Saturday at 10:02 AM (after 10:00-10:01 range)
        mock_now = datetime(
            2024, 1, 6, 10, 2, tzinfo=ZoneInfo("Europe/Paris")
        )  # Saturday
        mock_datetime.now.return_value = mock_now
        # Keep strptime working normally
        mock_datetime.strptime = datetime.strptime

        # Create a very short opening hour (10:00-10:01)
        short_opening = OpeningHour(
            day=6,  # Saturday
            ranges=[("10:00", "10:01")],
            enabled=True,
        )

        openings = [short_opening]

        result = is_clinic_open(openings)
        self.assertFalse(result)


if __name__ == "__main__":
    unittest.main()
