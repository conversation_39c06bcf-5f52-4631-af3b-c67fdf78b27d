from agent.base_agent import BaseAgent
from agent.questions_agent import \
    build_system_prompt as build_questions_system_prompt
from models.clinics import ClinicConfig
from utils.prompts import build_global_system_prompt, build_greeting_prompt


def build_system_prompt(clinic_config: ClinicConfig) -> str:
    return f"""
        {build_global_system_prompt(clinic_config)}
        
        # Instructions:
        
        Your responsibilities are limited to the following:
    
       
        1. Start by greeting the patient to the clinic and asking them how you can help them.
            - {build_greeting_prompt(clinic_config.name)}
            - Then, ask the patient how you can help them today
        2. Ask the patient for their full name.
        {build_questions_system_prompt(clinic_config, [])} exceptionally, no need to ask for the date of birth.
        
        **Do not address or inquire about any other topics**. If the user asks something unrelated, politely redirect them to one of the tasks above.
    """


class SimpleAgent(BaseAgent):
    name = "simple"

    def __init__(self, clinic_config: ClinicConfig):
        super().__init__(
            clinic_config=clinic_config, instructions=build_system_prompt(clinic_config)
        )
