import os

from dotenv import load_dotenv

from lib.http_client import HTTPClient

load_dotenv()

BOOKING_PROVIDER_URL = os.getenv("BOOKING_PROVIDER_URL")
BOOKING_PROVIDER_SECRET = os.getenv("BOOKING_PROVIDER_SECRET")


class BookingProviderClient(HTTPClient):
    def __init__(self) -> None:
        if not BOOKING_PROVIDER_URL or not BOOKING_PROVIDER_SECRET:
            raise ValueError("Missing Booking Provider credentials")

        super().__init__(
            base_url=BOOKING_PROVIDER_URL + "/api/v1",
            headers={"Authorization": f"Bearer {BOOKING_PROVIDER_SECRET}"},
        )


booking_provider_client = BookingProviderClient()
