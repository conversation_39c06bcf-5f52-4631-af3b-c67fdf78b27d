from typing import Op<PERSON>

from loguru import logger

from lib.booking_provider_client import booking_provider_client
from models.appointments import Appointment
from models.clinics import ClinicConfig
from models.user_data import UserData
from utils.appointments import build_demo_appointment


async def cancel_appointment(
    clinic_config: ClinicConfig,
    appointment_id: str,
    reason: Optional[str],
) -> None:
    if clinic_config.is_demo:
        return

    logger.info(f"Cancelling appointment {appointment_id} for {reason}")
    await booking_provider_client.put(
        "appointments/cancel",
        {
            "config": clinic_config.config_id,
            "id": appointment_id,
            "notes": f"[Vocca] {reason}",
        },
    )


async def reschedule_appointment(userdata: UserData) -> Appointment:
    if userdata.clinic_config.is_demo:
        return build_demo_appointment(userdata)

    if userdata.appointment is None or userdata.appointment_slot is None:
        raise ValueError("Missing appointment data for rescheduling")

    logger.info(
        f"Rescheduling appointment {userdata.appointment.id} from {userdata.appointment.start_date} to {userdata.appointment_slot.start_date}"
    )

    await cancel_appointment(
        userdata.clinic_config,
        userdata.appointment.id,
        f"Rendez-vous déplacé au {userdata.appointment_slot.start_date}",
    )
    return await book_appointment(userdata)


async def notify_delay(
    clinic_config: ClinicConfig,
    appointment_id: str,
    delay_minutes: int,
) -> None:
    if clinic_config.is_demo:
        return

    logger.info(
        f"Notifying delay of {delay_minutes} minutes for appointment {appointment_id}"
    )
    await booking_provider_client.put(
        "appointments",
        {
            "config": clinic_config.config_id,
            "id": appointment_id,
            "notes": f"[Vocca] Le patient aura {delay_minutes} minutes de retard.",
        },
    )


async def confirm_appointment(
    clinic_config: ClinicConfig,
    appointment_id: str,
) -> None:
    if clinic_config.is_demo:
        return

    await booking_provider_client.put(
        "appointments/confirm",
        {
            "config": clinic_config.config_id,
            "id": appointment_id,
            "notes": "[Vocca] Rendez-vous confirmé.",
        },
    )


class MissingDataBookingError(ValueError):
    def __init__(self, userdata: UserData):
        message = "Missing data for booking appointment"
        logger.warning(f"{message}: {userdata}")
        super().__init__(message)
        self.userdata = userdata


async def book_appointment(userdata: UserData) -> Appointment:
    if userdata.clinic_config.is_demo:
        return build_demo_appointment(userdata)

    if (
        not userdata.appointment_slot
        or not userdata.patient_identity
        or not userdata.motive
    ):
        logger.warning(
            f"Missing data for booking appointment: {userdata.appointment_slot}, {userdata.patient_identity}, {userdata.motive}"
        )
        raise MissingDataBookingError(userdata)

    new_appointment_data = {
        "call_id": userdata.call_config.call_id,
        "config": userdata.config_id,
        "patient_id": userdata.patient_identity.id,
        "first_name": userdata.patient_identity.first_name,
        "last_name": userdata.patient_identity.last_name,
        "birthdate": userdata.patient_identity.birthdate.strftime("%Y-%m-%d"),
        "phone_number": userdata.call_config.caller_phone_number,
        "visit_motive_id": userdata.motive.id,
        "agenda_id": userdata.appointment_slot.agenda_id,
        "equipment_agenda_id": userdata.appointment_slot.equipment_agenda_id,
        "start_date": userdata.appointment_slot.start_date.isoformat(),
        "end_date": userdata.appointment_slot.end_date.isoformat(),
        "practice_id": userdata.appointment_slot.practice_id,
        "steps": [
            step.model_dump(mode="json") for step in userdata.appointment_slot.steps
        ],
        "substitute_doctor_name": userdata.appointment_slot.substitute_doctor_name,  # TODO: also send doctor name and reconciliate this model with BookingProvider's
    }

    res = await booking_provider_client.post(
        url="appointments",
        json=new_appointment_data,
    )

    if res is None:
        raise RuntimeError(
            f"Failed to create new booking for call {userdata.call_config.call_id}"
        )

    new_appointment = Appointment(  # TODO: should we return 2 appointments when we use steps? For now, it's not used afterwards so it's fine.
        id=res.get("id"),
        patient_id=res.get("patient_id"),
        agenda_id=res.get("agenda_id"),
        start_date=res.get("start_date"),
        end_date=res.get("end_date"),
        motive=userdata.motive,
    )
    logger.info(f"Created new booking {new_appointment.id}")
    return new_appointment
