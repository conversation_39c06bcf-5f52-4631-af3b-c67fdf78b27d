from livekit.agents.metrics import (
    AgentMetrics,
    EOUMetrics,
    LLMMetrics,
    STTMetrics,
    TTSMetrics,
    VADMetrics
)
from loguru import logger


class LatencyLogger:
    def __init__(self) -> None:
        self.stt: STTMetrics | None = None
        self.vad: VADMetrics | None = None
        self.eou: EOUMetrics | None = None
        self.llm: LLMMetrics | None = None
        self.tts: TTSMetrics | None = None

    def aggregate_metrics(self, metrics: AgentMetrics) -> None:
        if isinstance(metrics, STTMetrics):
            self.stt = metrics
        if isinstance(metrics, VADMetrics):
            self.vad = metrics
        if isinstance(metrics, EOUMetrics):
            self.eou = metrics
        elif isinstance(metrics, LLMMetrics):
            self.llm = metrics
        elif isinstance(metrics, TTSMetrics):
            self.tts = metrics
            self.log_total_latency()

    def log_total_latency(self) -> None:
        total = (
            (
                self.eou.end_of_utterance_delay
                if self.eou and self.eou.end_of_utterance_delay
                else 0
            )
            + (
                self.vad.inference_duration_total
                if self.vad and self.vad.inference_duration_total
                else 0
            )
            + (self.llm.ttft if self.llm and self.llm.ttft else 0)
            + (self.tts.ttfb if self.tts and self.tts.ttfb else 0)
            + (self.stt.duration if self.stt and self.stt.duration else 0)
        )

        logger.info(
            f"🕒 Total Latency: {total:.3f}s "
            f"(EOU: {self.eou.end_of_utterance_delay if self.eou and self.eou.end_of_utterance_delay else 0:.3f}, "
            f"VAD: {self.vad.inference_duration_total if self.vad and self.vad.inference_duration_total else 0:.3f}, "
            f"LLM TTFT: {self.llm.ttft if self.llm and self.llm.ttft else 0:.3f}, "
            f"TTS TTFB: {self.tts.ttfb if self.tts and self.tts.ttfb else 0:.3f}, "
            f"STT: {self.stt.duration if self.stt and self.stt.duration else 0:.3f})",
            extra={"total_latency": total},
        )

        # Clear after logging
        self.eou = self.llm = self.tts = self.stt = self.vad = None
