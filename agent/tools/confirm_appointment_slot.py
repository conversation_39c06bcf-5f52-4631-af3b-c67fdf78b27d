from datetime import datetime
from typing import Annotated
from zoneinfo import ZoneInfo

import sentry_sdk
from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from models.user_data import RunContext_T


@function_tool()
async def confirm_appointment_slot(
    context: RunContext_T,
    appointment_datetime: Annotated[
        str,
        Field(
            description="The appointment slot datetime, using ISO format. It must necessarily be in the future."
        ),
    ],
) -> str:
    """
    Called to check the slot the patient wants to book is available
    """
    logger.info(f"Checking availability on {appointment_datetime}")

    if not context.userdata.patient_identity:
        return (
            "Patient identity is not confirmed. Collect it again before going further."
        )
    if not context.userdata.motive:
        return "Appointment motive is not confirmed. Collect it before going further."
    if not len(context.userdata.availabilities):
        return f"No availabilities have been fetched. Use `find_availabilities` with {appointment_datetime} before confirming this appointment slot."

    # Parse to naive datetime
    dt = datetime.fromisoformat(appointment_datetime)
    # Localize to Europe/Paris (UTC+2 in summer)
    dt = dt.replace(tzinfo=ZoneInfo("Europe/Paris"))

    # TODO: would be nice if we didn't need this. Monitor and remove if not needed
    if dt < datetime.now(tz=ZoneInfo("Europe/Paris")):
        dt = dt.replace(year=dt.year + 1)
        sentry_sdk.set_extra("appointment_datetime", appointment_datetime)
        sentry_sdk.capture_message("Appointment datetime in the past")

    found_availability = next(
        (a for a in context.userdata.availabilities if dt == a.start_date),
        None,
    )
    if not found_availability:
        return (
            f"Invalid appointment datetime: {appointment_datetime}. Please try again."
        )

    context.userdata.appointment_slot = found_availability

    doctor_name = f"Doctor {context.userdata.appointment_slot.doctor_name}"
    if context.userdata.appointment_slot.substitute_doctor_name:
        doctor_name = f"{context.userdata.appointment_slot.substitute_doctor_name} (substitute for {doctor_name})"

    return f"""
        Now let's confirm all the information before booking the appointment: 
        - Patient: {context.userdata.patient_identity.full_name}
        - Motive: {context.userdata.motive.name}
        - Date: {context.userdata.appointment_slot.start_date.strftime("%A %d %B %Y à %H:%M")}
        {f"- Doctor: {doctor_name}" if context.userdata.clinic_config.say_doctor_name else ""}
    """
