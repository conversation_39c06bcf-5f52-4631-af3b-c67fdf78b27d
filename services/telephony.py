import os
from typing import Optional

from livekit import api
from livekit.protocol.sip import TransferSIPParticipantRequest
from livekit.rtc import Room
from loguru import logger
from twilio.base.exceptions import TwilioRestException

from lib.twilio_client import INTERNAL_SERVER_URL, twilio_client
from utils.phone_numbers import format_phone_number


# TODO: probably not the final signature
async def forward_call(room: Room, forward_number: str) -> None:
    logger.info(f"Forwarding call to {forward_number}")

    formatted_forward_number = format_phone_number(forward_number)
    async with api.LiveKitAPI() as livekit_api:
        transfer_to = (
            f"sip:{formatted_forward_number}@{os.getenv('TWILIO_SIP_TRUNK_URI')}"
        )

        # Create transfer request
        _, caller = next(iter(room.remote_participants.items()))
        transfer_request = TransferSIPParticipantRequest(
            participant_identity=caller.identity,
            room_name=room.name,
            transfer_to=transfer_to,
            play_dialtone=True,
        )
        logger.info(f"Transfer request: {transfer_request} to {transfer_to}")

        # Transfer caller
        await livekit_api.sip.transfer_sip_participant(transfer_request)
        logger.info(f"Successfully transferred participant {caller.identity}")


async def fetch_twilio_recording_url(call_id: str) -> Optional[str]:
    """Fetches the recording URL for a given call ID."""
    try:
        recordings = twilio_client.recordings.list(call_sid=call_id)
        if recordings:
            recording_uri = recordings[0].uri
            recording_id = recording_uri.split("/")[-1].replace(".json", "")
            recording_url = f"{INTERNAL_SERVER_URL}/api/v1/twilio/listen/{recording_id}"
            return recording_url
    except TwilioRestException as e:
        logger.error(f"Failed to fetch recording: {str(e)}")

    logger.warning("No recording found for the call.")
    return None
