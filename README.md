# livekit-poc

## Installation

```shell
uv sync
uv pip install -r pyproject.toml
cp .env.example .env
```

### API Keys

**LiveKit keys**
1. Create an account on [LiveKit Cloud](https://cloud.livekit.io)
2. Under [Settings > Project Settings](https://cloud.livekit.io/projects/p_5rzkc7cwlno/settings/project), collect the project URL and copy paste it to `LIVEKIT_URL` (using wss:// protocol)
3. Under [Settings > API Keys](https://cloud.livekit.io/projects/p_5rzkc7cwlno/settings/keys) generate a new pair of key/secret and add it to your `.env` file

**Azure keys**
We will provide those keys for you, in a secure way.


### Run the project

```shell
uv run main.py dev
```

Then go to [LiveKit Agents Playground](https://agents-playground.livekit.io).
Your agent should connect to the playground room and start talking.
