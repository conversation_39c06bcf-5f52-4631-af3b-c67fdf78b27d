from typing import List

from models.clinics import OpeningHour

DAY_NAMES = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
]


def format_opening_hours(opening_hours: List[OpeningHour]) -> str:
    """
    This function formats the opening hours for display.
    Example:
        Monday: 08:15–18:00
        Tuesday: 09:30–12:00, 13:00–18:00
        Wednesday: 08:00–18:00
        Thursday: 08:00–18:00
        Friday: 09:30–12:00, 13:00–17:00
    """
    if opening_hours is None:
        return ""
    formatted_hours = []
    for opening_hour in sorted(
        opening_hours, key=lambda x: x.day % 7
    ):  # sort Sunday→Saturday
        day_index = opening_hour.day % 7
        day_name = (
            DAY_NAMES[day_index]
            if day_index < len(DAY_NAMES)
            else f"Day {opening_hour.day}"
        )
        if not opening_hour.enabled:
            formatted_hours.append(f"{day_name}: Closed")
            continue
        if not opening_hour.ranges:
            formatted_hours.append(f"{day_name}: Open")
            continue
        ranges_str = ", ".join([f"{start}-{end}" for start, end in opening_hour.ranges])
        formatted_hours.append(f"{day_name}: {ranges_str}")
    return "\n".join(formatted_hours)
