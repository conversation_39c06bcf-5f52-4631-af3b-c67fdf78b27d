from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel

from models.appointments import Appointment
from models.clinics import Agenda


class PatientIdentity(BaseModel):
    id: Optional[str] = None
    first_name: str
    last_name: str
    birthdate: date

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self) -> int:
        today = datetime.now().date()
        age = today.year - self.birthdate.year
        # Subtract 1 if birthday hasn't occurred this year
        if (today.month, today.day) < (self.birthdate.month, self.birthdate.day):
            age -= 1
        return age


class Patient(PatientIdentity):
    id: str
    email: Optional[str] = None
    # TODO: Could we find a way so it's not optional ? Let's be careful between existing patient and new patient
    #  - Existing patient might not have their phone number filled in Booking Provider
    #     - This number might be different from the caller phone number. We should decide whether we want to update it or keep the original one.
    #  - New patient will be identified with the caller phone number
    phone_number: Optional[str]
    appointments: List[Appointment]
    treating_doctor: Optional[Agenda] = None
    is_blocked: bool = False
