import tempfile
from typing import AsyncIterable

import requests
from livekit.agents import AudioConfig, BackgroundAudioPlayer, BuiltinAudioClip
from livekit.agents.utils.codecs import AudioStreamDecoder
from livekit.rtc import AudioFrame


def setup_background_audio() -> BackgroundAudioPlayer:
    return BackgroundAudioPlayer(
        # play office ambience sound looping in the background
        ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.7),
        # play keyboard typing sound when the agent is thinking
        thinking_sound=[
            AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING, volume=0.3),
            AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING2, volume=0.2),
        ],
    )


async def load_audio_file(audio_file_url: str) -> AsyncIterable[AudioFrame]:
    decoder = AudioStreamDecoder(sample_rate=48000, num_channels=1)

    with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as remote_file:
        remote_file.write(requests.get(audio_file_url).content)

        with open(remote_file.name, "rb") as local_file:
            while chunk := local_file.read(4096):
                decoder.push(chunk)
            decoder.end_input()

        async for frame in decoder:
            yield frame
