from datetime import date
from typing import Annotated

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from models.patients import PatientIdentity
from models.user_data import CallIntent, RunContext_T
from services.patients import (
    RetrievePatientRequest,
    fuzzy_retrieve_existing_patients
)
from utils.appointments import format_appointments


@function_tool()
async def confirm_identity(
    context: RunContext_T,
    first_name: Annotated[
        str,
        Field(
            description="The patient's first name, with correct spelling collected from the patient"
        ),
    ],
    last_name: Annotated[
        str,
        Field(
            description="The patient's last name, with correct spelling collected from the patient"
        ),
    ],
    birthdate: Annotated[
        date,
        Field(description="The patient's birthdate, collected from the patient"),
    ],
    is_existing_patient: Annotated[
        bool,
        Field(description="Whether the patient has already come to the clinic or not"),
    ],
) -> str:
    """
    Called when yoy have collected the patient's identity and need to confirm it.
    """
    logger.info(
        f"Confirmed patient identity: {first_name} {last_name} ({birthdate} | existing: {is_existing_patient})"
    )

    # Set patient identity
    patient_identity = PatientIdentity(
        first_name=first_name,
        last_name=last_name,
        birthdate=birthdate,
    )
    retrieved_patient = None

    # If patient already came to the clinic
    if is_existing_patient:
        # Do not fetch patient if we already have retrieved it
        if context.userdata.retrieved_patients:
            retrieved_patient = next(
                (
                    p
                    for p in context.userdata.retrieved_patients
                    if p.birthdate == birthdate
                ),
                None,
            )

        else:  # Retrieve patient in Doctolib and mark it as the retrieved patient
            retrieved_patients = await fuzzy_retrieve_existing_patients(
                context.userdata.clinic_config,
                params=RetrievePatientRequest(
                    first_name=first_name, last_name=last_name, birthdate=birthdate
                ),
            )
            if len(retrieved_patients) > 0:
                retrieved_patient = retrieved_patients[0]

        # Update patient identity with retrieved patient data
        if retrieved_patient:
            logger.info(
                f"Confirming retrieved patient identity: {retrieved_patient.id}"
            )
            patient_identity = retrieved_patient

    # Set context data
    context.userdata.patient_identity = patient_identity
    context.userdata.patient = retrieved_patient

    identity_prompt = f"""
        Patient identity confirmed. Keep this information for future usage but don't mention it to the patient.
        - Identity {first_name} {last_name} ({patient_identity.age} yo).
    """
    if retrieved_patient:
        if (
            retrieved_patient.treating_doctor
            and context.userdata.clinic_config.say_doctor_name
        ):
            identity_prompt += f"\n- Current treating doctor: {retrieved_patient.treating_doctor.name}."

        identity_prompt += f"""
            - Appointments ({len(retrieved_patient.appointments)}):
            {format_appointments(context.userdata.clinic_config, retrieved_patient.appointments)}
        """

    if context.userdata.intent == CallIntent.BOOKING:
        # Handle blocked patients
        if retrieved_patient and retrieved_patient.is_blocked:
            logger.info("Patient is blocked, ending call.")
            return """
                Politely tell the patient you they are not allowed to book appointment at this clinic.
                Tell them you will leave a note to the office.
                Then, end the conversation with `end_call` and `should_leave_note` set to `True`.
            """

        # Handle clinic restrictions
        clinic_config = context.userdata.clinic_config
        if clinic_config.min_age and patient_identity.age < clinic_config.min_age:
            return f"The patient is too young ({patient_identity.age} vs min_age: {clinic_config.min_age}) to book an appointment at this clinic. Propose to contact another clinic."
        if clinic_config.max_age and patient_identity.age > clinic_config.max_age:
            return f"The patient is too old ({patient_identity.age} vs max_age: {clinic_config.max_age}) to book an appointment at this clinic. Propose to contact another clinic."

        return f"""
            {identity_prompt}
            Now let's confirm the visit motive.
        """

    # Retrieved patient is necessary for other flows besides CallIntent.BOOKING
    if not retrieved_patient:
        logger.warning(f"Could not find patient for {patient_identity}")
        if (
            context.userdata.clinic_config.config_id == "config177"
        ):  # TODO: remove when working on forward config
            logger.warning("Forwarding call to reception as we could not find patient")
            return "Tell the patient you couldn't find their identity in our system and forward the call to the reception."
        return "Tell the patient you couldn't find their identity in our system. Repeat patient information and ask to correct the information to try again."

    return f"""
        {identity_prompt}
        Please confirm which appointment you want to act on.
    """
