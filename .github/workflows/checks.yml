name: Checks

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  lint:
    name: lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v5

      - name: Setup UV
        uses: astral-sh/setup-uv@v6
        with:
          version: latest
          enable-cache: true

      - name: Setup venv
        run: uv venv

      - name: Install dependencies
        run: uv pip install -r pyproject.toml

      - name: Setup ruff cache
        uses: actions/cache@v4
        with:
          path: .ruff_cache
          key: ${{ runner.os }}-ruff-${{ hashFiles('**/*.py') }}
          restore-keys: |
            ${{ runner.os }}-ruff-

      - name: Run ruff check
        run: uv run ruff check

  typecheck:
    name: typecheck
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v5

      - name: Setup UV
        uses: astral-sh/setup-uv@v6
        with:
          version: latest
          enable-cache: true

      - name: Setup venv
        run: uv venv

      - name: Install dependencies
        run: uv pip install -r pyproject.toml

      - name: Setup mypy cache
        uses: actions/cache@v4
        with:
          path: .mypy_cache
          key: ${{ runner.os }}-mypy-${{ hashFiles('**/*.py') }}
          restore-keys: ${{ runner.os }}-mypy-

      - name: Run mypy type check
        run: uv run mypy .

  test:
    name: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v5

      - name: Setup UV
        uses: astral-sh/setup-uv@v6
        with:
          version: latest
          enable-cache: true

      - name: Setup venv
        run: uv venv

      - name: Install dependencies
        run: uv pip install -r pyproject.toml

      - name: Setup pytest cache
        uses: actions/cache@v4
        with:
          path: .pytest_cache
          key: ${{ runner.os }}-pytest-${{ hashFiles('**/*.py') }}
          restore-keys: |
            ${{ runner.os }}-pytest-

      - name: Run tests
        run: uv run python -m pytest -v --tb=short

      - name: Generate test report
        if: always()
        run: uv run python -m pytest tests/ --tb=short --junit-xml=test-results.xml

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: test-results.xml
