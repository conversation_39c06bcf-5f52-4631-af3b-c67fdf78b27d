from typing import Annotated, List

import sentry_sdk
from livekit.agents.llm import function_tool
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from agent.tools.confirm_appointment_slot import confirm_appointment_slot
from agent.tools.confirm_doctor_selection import confirm_doctor_selection
from agent.tools.confirm_identity import confirm_identity
from agent.tools.find_availabilities import find_availabilities
from models.clinics import ClinicConfig, ClinicType
from models.patients import Patient
from models.user_data import RunContext_T
from services.appointments import MissingDataBookingError, book_appointment
from services.clinics import get_company_clinics
from utils.clinics import format_clinics, is_motive_available_at_clinic
from utils.motives import check_forbidden_motives_combination, format_motive
from utils.prompts import (
    END_OF_FLOW_PROMPT,
    IMAGING_END_OF_FLOW_INSTRUCTIONS,
    IMAGING_FLOW_BOOKING_PROMPT,
    build_global_system_prompt,
    build_name_prompt
)
from utils.strings import remove_text_in_parentheses


def build_select_doctor_prompt(clinic_config: ClinicConfig) -> str:
    doctors = [
        agenda for agenda in clinic_config.agendas if agenda.practitioner_id is not None
    ]

    if not clinic_config.select_doctor or not len(doctors):
        return "Note: You have no information about the doctors at the clinic."

    return f"""
        ### Step Doctor Selection
        Before looking up availabilities, we must confirm if the patient wants to book with a specific doctor.

        - If the patient is being treated by a specific doctor, ask them if they want to book with this doctor.
        - If the patient is not being treated by a specific doctor, ask them if they want to book with a specific doctor.
            - The patient must choose a doctor from the list below. No other options are available.
                - Do not list the full doctors list, unless the patient asks for it.
                - If the patient asks which doctors are available at the clinic, list a maximum of 3 doctors at random.
                - The patient has also the ability to not choose a specific doctor
                - If the patient mentions a doctor that you don't find on the list, don't say you didn't find it. Instead, find the closest matches and confirm with the patient.
            - Once they has chosen a doctor, call the `confirm_doctor_selection` tool with the doctor ID.
                - If the patient doesn't want a specific doctor, call the `confirm_doctor_selection` tool with `doctor_id` set to `None`.
        
        #### Available doctors:
        {"\n".join([f"  - {remove_text_in_parentheses(d.name)} (doctor_id: {d.id})" for d in doctors])}
    """


def build_select_clinic_prompt(clinic_config: ClinicConfig) -> str:
    company_clinics = get_company_clinics(clinic_config.config_name)

    return f"""
        ### Step Clinic Selection
        Before looking up availabilities, we must confirm which clinic the patient wants to book with. 
        The patient should choose a clinic from the list below. No other options are available.
        - If the patient asks which clinics are available, list only the clinics where the motive can be booked (check the motive's practice_ids field).
        - The patient doesn't need to choose a clinic if they have no preference.
        
        If the patient says they want to book with a specific clinic, call the `confirm_clinic_selection` tool with the chosen practice_id.
        - If the patient doesn't want a specific clinic, call the `confirm_clinic_selection` tool with `practice_id` set to `None`.

        #### Available clinics:
        {format_clinics(company_clinics)}
    """


def build_system_prompt(
    clinic_config: ClinicConfig, retrieved_patients: List[Patient]
) -> str:
    motive_collection_custom_prompt = ""
    if clinic_config.motive_collection_prompt:
        motive_collection_custom_prompt = f"""
            ### Special cases when collecting the motive:
            {clinic_config.motive_collection_prompt}
        """

    return f"""
        {build_global_system_prompt(clinic_config)}
      
         ---
    
        # Booking Flow 
        Your goal is to **guide the patient through booking a medical appointment** from start to finish, strictly following the steps below. Immediately start the flow (no introductions) and lead the patient efficiently and empathetically.
        
        The appointment booking succeeds only after you've called the `book_appointment` tool with all required details and received the patient's final confirmation.
        
        ## Step 1. Patient identity collection
        {build_name_prompt(retrieved_patients)}
    
        ## Step 2. Motive Collection  
        Determine the **motive (reason)** for the appointment and confirm it with the patient.  
        - **Check for Existing Motive Info**: The patient may have already mentioned why they are calling. If you have a hint of the motive from earlier in the conversation, verify it. For example: *"Vous souhaitez prendre rendez-vous pour **une consultation de routine**, c’est bien cela ?"*. Only do this if you’re fairly sure; if not, just ask.  
        - **Ask for the Motive**: If the motive is unclear or not given yet, ask an open question: *"Quel est le **motif** de votre consultation ?"*. (Do **not** list all possible motives unsolicited, as that could overwhelm the user.)  
        - **Acceptable Motives**: You have a predefined list of possible motives Only those are valid. 
            - **First Appointments**: For new patients to the clinic, as defined in the identity collection step, always offer the "first appointment" motive as an option when available, even when the patient doesn't explicitly ask for it and the requested motive is in the list.
                - Example in French: It's very common that clinic split similar motive between "Consultation de suivi de ..." and "Première consultation de ...". If the patient is a new patient, always propose the "Première consultation de ..." motive.
            - **Adult / Child Motives**: If the patient is a child, always propose the "child" version of the motive when available, even when the patient doesn't explicitly ask for it and the requested motive is in the list.
                - Example in French: It's very common that the clinic split similar motive between "Consultation enfant" (or even "Consultation enfant de moins de 6 ans") and "Consultation adulte". If the patient is a child, the closest matching motive.
            - **Hints**: Some of the motives have specific hints to help identify them. You can use them to guide your questioning.
            - If the patient’s answer doesn’t match one of the known motives, do not invent a new one
                - Instead, politely clarify or offer a close match. For instance, if they say something that isn’t on the list, respond with something like: *"Je ne suis pas sûr de comprendre. Est-ce pour **[closest motive]** ?"* or ask them to rephrase. 
            - If they ask "What can I book?", provide a **short** list of a few example motives relevant to what they hinted, up to 5 options at most.  
        - **Explicit Motive Confirmation**: Once you have identified a motive that matches one of the available options, **repeat it back to the patient for confirmation**. For example: *"Très bien, ce serait pour **un bilan sanguin**, c’est bien ça ?"*. Wait for the patient to confirm with a yes (or correct you if wrong). **Do not proceed to searching for appointments** until the patient clearly confirms the motive. This is crucial to ensure you’re booking the right type of appointment.  
        - After confirmation, call the `confirm_motive` tool with the chosen motive to log it, and acknowledge the confirmation (e.g., "*Parfait, nous allons planifier un rendez-vous pour **ce motif**.*").
        
        {IMAGING_FLOW_BOOKING_PROMPT if clinic_config.clinic_type == ClinicType.IMAGING else ""}
        
        {motive_collection_custom_prompt}
        
        ### Available motives: 
        {"\n".join([format_motive(m) for m in clinic_config.motives])}
        
        ## Step 3. Appointment Scheduling  
        {build_select_clinic_prompt(clinic_config) if clinic_config.select_clinic else ""}
        
        {build_select_doctor_prompt(clinic_config)}

        Now, find an available date and time that suits the patient’s needs, and guide them to book it.
        - **Retrieve Availabilities**: Let the patient know you will check the schedule (e.g., "*Un instant, je vérifie les disponibilités...*"). Use the `find_availabilities` tool to fetch the nearest available appointment slots for the confirmed motive and (implicitly) for this patient.
        - **Present Options Clearly**: When the availability results return, present a summary of a few options in a **clear and concise** manner. Group similar times or days together so that the patient can easily grasp the choices. However, do not propose more than 2 groups:
            - For example, you might say: *"Les prochaines disponibilités sont **lundi et mercredi en fin d'après-midi (18h ou 19h)**, ou **mardi entre 15h00 et 16h30**."* This gives an intuitive overview rather than a long list.
            - If there are many options, prioritize a selection of the most reasonable ones (e.g., the earliest dates or times that were found, or those the patient hinted at preferring). You can always offer to look further if none of these suit the patient.
            - When listing availabilities with a specific doctor, mention the substitute doctor if applicable.
            - If there are no options, don't invent anything, apologize and ask the patient to call back later.
        - **Handle Specific Date/Time Requests**: If the patient asks for a **specific date or time** (e.g., "Do you have something on August 1st in the morning?" or "I’m only free after 6pm"), use the `find_availabilities` tool with the provided constraint (that date or time window) to check.
            - If there is availability matching the request, present those specific slots (e.g., "*Oui, le **1er août à 10h30** est disponible.*").
            - If there is no exact match (the requested day/time is fully booked or not possible), apologize and clearly state that *no* slots are available then. Then **proactively offer the closest alternatives**. For example: *"Désolé, il n’y a pas de disponibilité le **1er août à 11h**. Par contre, je peux vous proposer le **1er août à 14h00**, ou bien le **2 août** dans la matinée.*"*. This way, the patient isn’t left hanging with a denial – you immediately provide options near their preference.
        - **Iterate if Needed**: Give the patient a moment to consider the options. They might choose one, ask about a different day, or indicate none of the options work. Respond accordingly:
            - If they want a different time or day that wasn’t listed, happily oblige to search again with the new criteria (call `find_availabilities` again with the updated request).
            - If they seem unsure, you can gently ask which days or times generally work best and offer to find slots in that range.
        - **Confirm Selected Slot**: Once the patient selects a specific slot (date and time), repeat it back to them to make sure you have it right. For example: *"D’accord, nous partons sur le **mardi 5 septembre à 16h30**. Cela vous convient-il ?"*. If they confirm, proceed to lock in that slot by calling the `confirm_appointment_slot` tool with that date and time. This reserves the time in the system (pending final booking).

        ## Step 4. Final Confirmation and Booking
        Now finalize the appointment booking and make sure the patient has all the details:
        - **Review Details**: Clearly recap in a single, human-readable sentence all the information that has been gathered and decided before booking. This includes: the patient’s name, the motive, {"the doctor name" if clinic_config.say_doctor_name else ""} and the appointment date/time
            - Example, in French: "Parfait. j’ai bien noté que vous souhaitez un bilan orthoptique {"avec le docteur Dupont" if clinic_config.say_doctor_name else ""} le 20 janvier à 14h pour Alexandre Dubois. Est-ce que tout est correct?"
        - **Final Corrections**: Wait for the patient to confirm that this summary is all correct. If the patient notices any mistake or wants to change something (perhaps they misheard a date or need to adjust time), **address it immediately**:
            - If it’s a minor detail (e.g., spelling of name or a wrong birth year), fix that detail by revisiting the relevant step (without making them repeat everything).
            - If they want a different slot now, return to the scheduling step to find a better time.
            - Ensure that after any change, you reconfirm the detail with the patient.
        - **Book the Appointment**: Once the patient gives a final confirmation that all details are correct and they are ready to book, call the `book_appointment` tool with the confirmed information (patient identity, motive, date/time, etc.). This will finalize the appointment in the system.
        - **Confirmation to Patient**: After a successful booking, inform the patient in a friendly manner. 
            - For example: *"C’est bon, votre rendez-vous est **confirmé** ! Vous recevrez un SMS de confirmation d’ici quelques instants."*.
        
        ## Step 5. End of flow
        {END_OF_FLOW_PROMPT}
        
        ## Special case: Multiple appointments booking
        The patient might mention that they need multiple appointments. In that case, propose to book appointments one by one.
        - You **must** treat each appointment sequentially. Ignore any mention of additional appointments until the current one is booked.
        - In other words, you must complete the booking process (ie call the `book_appointment` tool) for the first appointment before starting the process for the second appointment.
    """


class BookingAgent(BaseAgent):
    name = "booking"

    def __init__(self, clinic_config: ClinicConfig, retrieved_patients: List[Patient]):
        self.lock = False
        tools = [
            confirm_identity,
            find_availabilities,
            confirm_appointment_slot,
        ]
        if clinic_config.select_doctor:
            tools.append(confirm_doctor_selection)

        super().__init__(
            clinic_config=clinic_config,
            instructions=build_system_prompt(clinic_config, retrieved_patients),
            tools=tools,
        )

    @function_tool()
    async def confirm_motive(
        self,
        context: RunContext_T,
        motive_id: Annotated[
            int,
            Field(
                description="The patient's appointment motive ID collected from the list of available motives"
            ),
        ],
        is_secondary_motive: Annotated[
            bool,
            Field(
                description="Whether this motive is the secondary motive or the primary one for imaging motives"
            ),
        ],
    ) -> str:
        """
        Called after you confirmed the patient's visit motive and they said yes.
        """

        logger.info(
            f"Confirming {'secondary' if is_secondary_motive else ''} visit motive: {motive_id}"
        )

        if not context.userdata.patient_identity:
            return "Patient identity is not confirmed. Collect it again before finding availabilities."

        found_motive = next(
            (m for m in context.userdata.clinic_config.motives if m.id == motive_id),
            None,
        )

        if not found_motive:
            return f"Invalid motive: {motive_id}. Please try again."
        logger.info(
            f"Found motive: {found_motive.name} ({found_motive.id} | open: {found_motive.open} | practice_ids: {found_motive.practice_ids} )"
        )

        # Motive constraints
        if found_motive.should_transfer:
            return """
                This motive requires special attention. Inform the patient that this motive requires special attention. Forward the call to the reception by using `forward_call`
            """

        if not found_motive.open:
            # TODO: should we leave a note if the patient books an appointment anyway
            return """
                The patient is not allowed to book an appointment for this motive:
                - Inform them that this motive requires a particular attention and you will leave a note to the reception for the patient to be contacted by the clinic. 
            """

        if (
            found_motive.max_age is not None
            and context.userdata.patient_identity.age > found_motive.max_age
        ):
            return f"The patient is too old ({context.userdata.patient_identity.age} vs max_age: {found_motive.max_age}) for this motive. Propose the patient to select another motive."
        if (
            found_motive.min_age is not None
            and context.userdata.patient_identity.age < found_motive.min_age
        ):
            return f"The patient is too young ({context.userdata.patient_identity.age} vs min_age: {found_motive.min_age}) for this motive. Propose the patient to select another motive."

        if (
            context.userdata.clinic_config.select_clinic
            and context.userdata.practice_id
            and not is_motive_available_at_clinic(
                found_motive, context.userdata.practice_id
            )
        ):
            return f"This motive is not available at the clinic where the patient wants to book. Propose the patient to select another motive or to select another clinic. (motive practice_ids: {found_motive.practice_ids}, selected clinic: {context.userdata.practice_id})"

        if found_motive.instructions:
            await self.session.say(found_motive.instructions)

        if (
            is_secondary_motive
            and context.userdata.motive
            and context.userdata.clinic_config.clinic_type == ClinicType.IMAGING
        ):
            if check_forbidden_motives_combination(
                context.userdata.motive, found_motive
            ):
                logger.warning(
                    f"Forbidden motives combination: {context.userdata.motive.name} + {found_motive.name}"
                )
                return "Explain the patient it is not possible to book those two motives on the same day. Then call `forward_call` to transfer the patient to a human agent."

            if (
                not context.userdata.secondary_motive
                or context.userdata.secondary_motive.id != found_motive.id
            ):
                context.userdata.secondary_motive = found_motive
                context.userdata.availabilities = []
        elif (
            not context.userdata.motive or context.userdata.motive.id != found_motive.id
        ):
            context.userdata.motive = found_motive
            context.userdata.availabilities = []

        follow_up_instructions = ["Now let's continue the booking flow by:"]
        if (
            context.userdata.clinic_config.clinic_type == ClinicType.IMAGING
            and "irm" in found_motive.name.lower()
        ):
            follow_up_instructions.append(
                """
                - Asking the patient if they have any metal in their body
                    - Example in French: "Avez-vous un objet métallique dans le corps ? Par exemple un pacemaker, une valve cardiaque ou un capteur de glycémie ?"
                    - Do not ask again if already asked in the conversation
                    - If the patient answers yes or doesn't know or seems hesitant, tell them that for security reason you are not allowed to proceed with the booking but you will leave a note to the reception and end the call.
            """
            )
        if (
            context.userdata.clinic_config.clinic_type == ClinicType.IMAGING
            and not is_secondary_motive
        ):
            follow_up_instructions.append(
                "- Confirming if the patient needs a second exam and if not,"
            )
        if context.userdata.clinic_config.select_doctor:
            follow_up_instructions.append(
                "- Confirming with which doctor the patient wants to book with"
            )
        else:
            follow_up_instructions.append("- Finding availabilities for this motive")

        return "\n- ".join(follow_up_instructions)

    @function_tool()
    async def book_appointment(
        self,
        context: RunContext_T,
    ) -> str:
        """
        Called you have gathered all the necessary informations to actually book the appointment
        """
        logger.info(
            f"Booking appointment for {context.userdata.patient_identity} for {context.userdata.motive} on {context.userdata.appointment_slot} (lock: {self.lock})"
        )
        if self.lock:
            return "A booking is already in progress. Ignore this function call, don't tell anything to the patient."

        if not context.userdata.patient_identity:
            return "Patient identity is not confirmed. Collect it again before booking."
        if not context.userdata.motive:
            return (
                "Appointment motive is not confirmed. Collect it again before booking."
            )
        if not context.userdata.appointment_slot:
            return "Appointment datetime is not confirmed. Collect it again before booking."

        try:
            self.lock = True  # TODO: should we generalize this lock behavior to all end-flow tool calls? With a decorator or a private method?
            context.userdata.appointment = await book_appointment(context.userdata)
            await context.userdata.audio_player.play("./assets/ding.wav")
            context.userdata.is_call_successful = True
            # Reset userdata to avoid double booking
            context.userdata.motive = None
            context.userdata.secondary_motive = None
            context.userdata.appointment_slot = None

            confirmed_booking_instructions = [
                "Tell the patient the booking is confirmed and that they will receive a confirmation SMS."
            ]
            if (
                context.userdata.clinic_config.clinic_type == ClinicType.IMAGING
                and context.userdata.clinic_config.booking_provider_type == "DOCTOLIB"
            ):
                confirmed_booking_instructions.append(IMAGING_END_OF_FLOW_INSTRUCTIONS)
            if context.userdata.clinic_config.confirmed_booking_instructions:
                confirmed_booking_instructions.append(
                    context.userdata.clinic_config.confirmed_booking_instructions
                )
            return f"""
                The booking is confirmed:
                {"\n- ".join(confirmed_booking_instructions)}
            """
        except MissingDataBookingError:
            return "This booking has already been processed. Ignore this function call, don't tell anything to the patient."
        except Exception as e:
            sentry_sdk.capture_exception(e)
            logger.error(f"Failed to book appointment: {e}")
            return "An error occurred while booking the appointment. Please try again."
        finally:
            self.lock = False
