import asyncio
from datetime import date, datetime
from typing import Any, List, Optional

import sentry_sdk
from loguru import logger
from pydantic import BaseModel

from lib.booking_provider_client import booking_provider_client
from models.clinics import Agenda, ClinicConfig
from models.patients import Patient
from utils.appointments import build_appointments
from utils.strings import remove_text_in_parentheses


class RetrievePatientRequest(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    birthdate: Optional[date] = None
    phone_number: Optional[str] = None


def build_patient(raw_patient: Any, clinic_config: ClinicConfig) -> Patient:  # type: ignore
    patient_id = raw_patient.get("id")
    patient_birthdate = raw_patient.get("birthdate")
    raw_appointments = raw_patient.get("appointments", [])

    if (
        not patient_birthdate
    ):  # TODO: should not happen or be handled in Booking Provider.
        sentry_sdk.set_tag("patient_id", patient_id)
        sentry_sdk.capture_message("No birthdate in Booking Provider patient response")
        patient_birthdate = date(
            1995, 1, 1
        )  # FIXME: https://linear.app/vocca-ai/issue/VOC-84/ask-patients-birthdate-when-empty-in-booking-provider

    appointments = build_appointments(
        patient_id, raw_appointments, clinic_config.motives
    )

    treating_doctor = None
    retrieved_doctor = raw_patient.get("medecin_historique")
    if retrieved_doctor and retrieved_doctor.get("practitioner_id"):
        treating_doctor = Agenda(
            id=retrieved_doctor.get("id"),
            name=remove_text_in_parentheses(retrieved_doctor.get("name")),
            practitioner_id=retrieved_doctor.get("practitioner_id"),
            practice_id=retrieved_doctor.get("practice_id"),
        )

    return Patient(
        id=patient_id,
        last_name=raw_patient.get("last_name"),
        first_name=raw_patient.get("first_name"),
        birthdate=patient_birthdate
        or datetime.now().date(),  # TODO: should we ask the patient birthdate if not in doctolib?
        phone_number=raw_patient.get("phone_number"),
        email=raw_patient.get("email"),
        appointments=appointments,
        treating_doctor=treating_doctor,
        is_blocked=bool(raw_patient.get("bounced_at")),
    )


async def retrieve_existing_patients(
    clinic_config: ClinicConfig, params: RetrievePatientRequest
) -> List[Patient]:
    logger.info(f"Retrieving patient details from {params}")
    res = await booking_provider_client.post(
        "patients/search",
        {
            "config": clinic_config.config_id,
            "first_name": params.first_name,
            "last_name": params.last_name,
            "birthdate": (
                params.birthdate.strftime("%Y-%m-%d") if params.birthdate else None
            ),
            "phone_number": params.phone_number,
        },
    )

    if not isinstance(res, list) or len(res) == 0:
        logger.warning(f"Did not find patient for {params}")
        return []

    logger.info(f"Found {len(res)} patients for {params}")
    return [build_patient(p, clinic_config) for p in res]


# TODO: Let's check if this improved our patient identity retrieval
async def fuzzy_retrieve_existing_patients(
    clinic_config: ClinicConfig, params: RetrievePatientRequest
) -> List[Patient]:
    patients = await retrieve_existing_patients(clinic_config, params)
    if len(patients) > 0:
        return patients

    logger.warning(f"No patient match for {params}. Trying fuzzy matching")
    tasks = [
        retrieve_existing_patients(
            clinic_config,
            params.model_copy(update={"first_name": None}),
        ),
        retrieve_existing_patients(
            clinic_config,
            params.model_copy(update={"last_name": None}),
        ),
    ]
    results = await asyncio.gather(*tasks)
    patients = [patient for sublist in results for patient in sublist]
    logger.info(
        f"Found {len(patients)} patients for {params} with fuzzy matching: {', '.join([p.full_name for p in patients])}"
    )
    if len(patients) > 1:
        sentry_sdk.set_context("retrieval_params", params.model_dump(mode="json"))
        sentry_sdk.set_context(
            "retrieved_patients",
            {"patients": [p.model_dump(mode="json") for p in patients]},
        )
        sentry_sdk.capture_message("Multiple patients found with fuzzy matching")
        return []  # TODO: fuzzywuzzy filters
    return patients
