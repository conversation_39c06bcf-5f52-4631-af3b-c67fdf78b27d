from typing import List

from models.clinics import ClinicConfig
from models.motives import Motive
from utils.strings import remove_accents


def format_motive(motive: Motive) -> str:
    motive_id = f"motive_id: {motive.id}"

    # Optional age limit
    age_limit = "age_limit: "
    if motive.min_age is not None and motive.max_age is not None:
        age_limit += f"from {motive.min_age} to {motive.max_age} years"
    elif motive.min_age is not None:
        age_limit += f"from {motive.min_age} years"
    elif motive.max_age is not None:
        age_limit += f"up to {motive.max_age} years"
    else:
        age_limit = None

    # Optional hint
    hint = f"hint: {motive.hint}" if motive.hint else ""

    motive_metadata = " | ".join(
        [meta for meta in [motive_id, age_limit, hint] if meta]
    )
    return f"  - {motive.name} ({motive_metadata})"


def format_motives_by_clinic(clinics: List[ClinicConfig]) -> str:
    if len(clinics) == 1:
        return "\n".join([format_motive(m) for m in clinics[0].motives])

    motives = []
    for clinic in clinics:
        clinic_motives = [f"Motives at {clinic.name} (clinic_id: {clinic.clinic_id})"]
        for motive in clinic.motives:
            clinic_motives.append(format_motive(motive))
        motives.append("\n".join(clinic_motives))

    return "\n\n".join(motives)


FORBIDDEN_MOTIVES_COMBINATIONS = [
    ["irm", "irm"],
    ["echo", "echo"],
    ["scanner", "scanner"],
]


# TODO: let's not use strings checks and use hardcoded data once configuration is refactored
def check_forbidden_motives_combination(motive_1: Motive, motive_2: Motive) -> bool:
    clean_motive_1 = remove_accents(motive_1.name.lower())
    clean_motive_2 = remove_accents(motive_2.name.lower())

    for motive_lookups in FORBIDDEN_MOTIVES_COMBINATIONS:
        if (
            motive_lookups[0] in clean_motive_1 and motive_lookups[1] in clean_motive_2
        ) or (
            motive_lookups[1] in clean_motive_1 and motive_lookups[0] in clean_motive_2
        ):
            return True
    return False
